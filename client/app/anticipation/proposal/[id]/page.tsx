"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  ChevronLeft,
  X,
  CheckCircle2,
  XCircle,
  Award,
  DollarSign,
  Calendar,
  Sparkles,
} from "lucide-react"
import Image from "next/image"

interface Proposal {
  id: string
  netAmount: number
  grossAmount: number
  feeAmount: number
  feePercentage: number
  anticipationMonths: number
  monthlyRent: number
}

interface RentalAdvance {
  id: string
  status: string
  proposal: Proposal
}

export default function ProposalPage({ params }: { params: { id: string } }) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [rentalAdvance, setRentalAdvance] = useState<RentalAdvance | null>(null)
  const [accepting, setAccepting] = useState(false)

  useEffect(() => {
    const loadProposal = async () => {
      try {
        const authData = localStorage.getItem("x-auth-state")
        if (!authData) {
          window.location.href = "/"
          return
        }

        const { authToken } = JSON.parse(authData)

        const response = await fetch(`/api/v1/rental-advance/${params.id}/proposal`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        })

        if (response.ok) {
          const data = await response.json()
          setRentalAdvance(data)
        } else {
          setError("Erro ao carregar proposta")
        }
      } catch (error) {
        console.error("Error loading proposal:", error)
        setError("Erro de conexão. Tente novamente.")
      } finally {
        setLoading(false)
      }
    }

    loadProposal()
  }, [params.id])

  const acceptProposal = async () => {
    setAccepting(true)
    setError("")
    setSuccess("")

    try {
      const authData = localStorage.getItem("x-auth-state")
      if (!authData) {
        window.location.href = "/"
        return
      }

      const { authToken } = JSON.parse(authData)

      const response = await fetch("/api/v1/rental-advance/accept-proposal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          rentalAdvanceId: params.id,
        }),
      })

      if (response.ok) {
        setSuccess("Proposta aceita! Redirecionando para confirmação...")
        setTimeout(() => {
          window.location.href = `/anticipation/confirmation/${params.id}`
        }, 1500)
      } else {
        const result = await response.json()
        setError(result.message || "Erro ao aceitar proposta")
      }
    } catch (error) {
      console.error("Error accepting proposal:", error)
      setError("Erro de conexão. Tente novamente.")
    } finally {
      setAccepting(false)
    }
  }

  const rejectProposal = () => {
    // For now, just redirect back to dashboard
    // In the future, this could show a rejection dialog
    window.location.href = "/dashboard"
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Carregando proposta...</p>
        </div>
      </div>
    )
  }

  if (!rentalAdvance?.proposal) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center">
          <p className="text-white mb-4">Proposta não encontrada</p>
          <Button
            onClick={() => window.location.href = "/dashboard"}
            className="bg-white text-blue-600 hover:bg-gray-100"
          >
            Voltar ao Dashboard
          </Button>
        </div>
      </div>
    )
  }

  const { proposal } = rentalAdvance

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 px-4 pt-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = `/anticipation/validation/${params.id}`}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Voltar"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/dashboard"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Fechar"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="px-4">
        <div className="text-center mb-6">
          <div className="text-4xl mb-4">🎉</div>
          <h1 className="text-2xl font-bold text-white mb-2">Temos uma Proposta!</h1>
          <p className="text-white/90 text-sm">
            Sua antecipação foi <span className="font-bold">pré-aprovada</span>!
          </p>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm mb-4">
            {error}
          </div>
        )}
        {success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm mb-4">
            {success}
          </div>
        )}

        <Card className="bg-white shadow-xl rounded-2xl border-0 mb-6">
          <CardContent className="space-y-4 p-6">
            <div className="text-center">
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-emerald-200 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Award className="h-5 w-5 text-emerald-600" />
                  <span className="text-emerald-700 font-semibold text-sm">Proposta Especial</span>
                </div>
                <p className="text-3xl font-bold text-emerald-800 mb-1">
                  {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(proposal.netAmount)}
                </p>
                <p className="text-emerald-600 font-medium text-sm">Valor líquido a receber</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="p-3 bg-gradient-to-br from-slate-50 to-slate-100 rounded-lg border border-slate-200">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="h-4 w-4 text-slate-600" />
                  <span className="text-xs text-slate-600 font-medium">Aluguel Mensal</span>
                </div>
                <div className="text-center">
                  <p className="text-lg font-bold text-blue-600">
                    {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(proposal.monthlyRent)}
                  </p>
                </div>
              </div>
              <div className="p-3 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg border border-indigo-200">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-indigo-600" />
                  <span className="text-xs text-indigo-600 font-medium">Meses Antecipados</span>
                </div>
                <p className="text-lg font-bold text-center text-blue-600">
                  {proposal.anticipationMonths} meses
                </p>
              </div>
            </div>

            {/* Breakdown */}
            <div className="border-t pt-4 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Valor bruto:</span>
                <span className="font-bold text-[#0B4375]">
                  {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(proposal.grossAmount)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Taxa de serviço ({proposal.feePercentage}%):</span>
                <span className="font-bold text-red-600">
                  -{new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(proposal.feeAmount)}
                </span>
              </div>
              <div className="flex justify-between items-center border-t pt-3">
                <span className="text-lg font-bold text-gray-800">Valor líquido:</span>
                <span className="text-lg font-bold text-green-600">
                  {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(proposal.netAmount)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-3">
          <Button
            onClick={acceptProposal}
            className="w-full h-12 text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
            disabled={accepting}
          >
            <CheckCircle2 className="w-4 h-4" />
            {accepting ? "ACEITANDO..." : "SIM, ACEITO A PROPOSTA!"}
          </Button>
          <Button 
            onClick={rejectProposal} 
            className="w-full h-10 text-sm font-medium bg-red-50 border border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300 transition-all duration-200 rounded-lg"
          >
            <XCircle className="w-4 h-4 mr-2" />
            Não, recusar proposta
          </Button>
        </div>

        <div className="mt-6 p-4 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-white" />
            </div>
            <h3 className="text-white font-bold">Próximos Passos</h3>
          </div>
          <ul className="text-white/90 text-sm space-y-2">
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
              Confirmação dos dados bancários
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
              Upload de documentos complementares
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
              Transferência em até 24h úteis
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
