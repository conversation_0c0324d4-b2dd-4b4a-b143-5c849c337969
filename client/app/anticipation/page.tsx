"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { useF<PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DollarSign,
  CalendarDays,
  Building,
  Upload,
  ArrowRight,
  ChevronLeft,
  X,
  Zap,
  Lock,
  ExternalLink,
} from "lucide-react"
import Image from "next/image"
import Fuse from "fuse.js"

// Validation schemas
const anticipationSchema = z.object({
  valorAluguelLiquido: z.string().min(1, "Valor do aluguel é obrigatório"),
  mesesAntecipacao: z.string().min(1, "Número de meses é obrigatório"),
  imobiliaria: z.string().min(1, "Digite o nome da imobiliária"),
  contratoFileName: z.string().min(1, "Upload do contrato é obrigatório"),
  dataConsent: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
})

interface RealEstate {
  id: string
  name: string
}

export default function AnticipationPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [realEstates, setRealEstates] = useState<RealEstate[]>([])
  const [user, setUser] = useState<any>(null)
  const [filteredRealEstates, setFilteredRealEstates] = useState<RealEstate[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedRealEstateId, setSelectedRealEstateId] = useState("")
  const [isValidRealEstate, setIsValidRealEstate] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const fuse = useRef<Fuse<RealEstate> | null>(null)

  const form = useForm<z.infer<typeof anticipationSchema>>({
    resolver: zodResolver(anticipationSchema),
    defaultValues: {
      valorAluguelLiquido: "",
      mesesAntecipacao: "",
      imobiliaria: "",
      contratoFileName: "",
      dataConsent: false,
    },
  })

  // Check authentication and load user data
  useEffect(() => {
    const authData = localStorage.getItem("x-auth-state")
    if (!authData) {
      window.location.href = "/"
      return
    }

    try {
      const { user: userData } = JSON.parse(authData)
      setUser(userData)
    } catch (error) {
      console.error("Error parsing auth data:", error)
      window.location.href = "/"
    }
  }, [])

  // Load real estates
  useEffect(() => {
    const loadRealEstates = async () => {
      try {
        const response = await fetch("/api/v1/real-estate")
        
        if (response.ok) {
          const data = await response.json()
          setRealEstates(data)
          
          // Configure Fuse.js for fuzzy search
          fuse.current = new Fuse(data, {
            keys: ['name'],
            threshold: 0.4,
            includeScore: true,
            minMatchCharLength: 2,
          })
        } else {
          console.error("Failed to load real estates:", response.status)
        }
      } catch (error) {
        console.error("Error loading real estates:", error)
      }
    }

    loadRealEstates()
  }, [])

  // Função para buscar imobiliárias com fuzzy search
  const searchRealEstates = (query: string) => {
    console.log("Searching for:", query)
    console.log("Available real estates:", realEstates.length)
    console.log("Fuse instance:", fuse.current)
    
    // Reset validation state when user types
    setIsValidRealEstate(false)
    setSelectedRealEstateId("")
    
    if (!query.trim() || !fuse.current) {
      console.log("No query or fuse instance")
      setFilteredRealEstates([])
      setShowSuggestions(false)
      return
    }

    const results = fuse.current.search(query)
    console.log("Search results:", results)
    const matches = results.map(result => result.item)
    console.log("Matches:", matches)
    setFilteredRealEstates(matches)
    setShowSuggestions(true)
  }

  // Função para selecionar uma imobiliária
  const selectRealEstate = (realEstate: RealEstate) => {
    form.setValue("imobiliaria", realEstate.name)
    setSelectedRealEstateId(realEstate.id)
    setIsValidRealEstate(true)
    setShowSuggestions(false)
    setFilteredRealEstates([])
  }

  // Função para validar imobiliária no backend
  const validateRealEstate = async (realEstateId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/v1/real-estate/${realEstateId}`)
      return response.ok
    } catch (error) {
      console.error("Error validating real estate:", error)
      return false
    }
  }

  // Função para abrir WhatsApp
  const openWhatsApp = () => {
    const imobiliariaName = form.watch("imobiliaria")
    const message = encodeURIComponent(
      `Olá! Gostaria de solicitar que a imobiliária "${imobiliariaName}" seja adicionada ao sistema LocPay. Obrigado!`
    )
    const whatsappUrl = `https://wa.me/5585999666347?text=${message}`
    window.open(whatsappUrl, '_blank')
  }

  const maskCurrency = (value: string) => {
    const numericValue = value.replace(/\D/g, "")
    const formattedValue = (Number(numericValue) / 100).toLocaleString("pt-BR", {
      style: "currency",
      currency: "BRL",
    })
    return formattedValue
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && file.type === "application/pdf") {
      form.setValue("contratoFileName", file.name)
      setError("")
    } else if (file) {
      setError("Por favor, envie um arquivo PDF.")
      form.setValue("contratoFileName", "")
    } else {
      form.setValue("contratoFileName", "")
    }
  }

  const onSubmit = async (data: z.infer<typeof anticipationSchema>) => {
    setLoading(true)
    setError("")
    setSuccess("")

    try {
      const authData = localStorage.getItem("x-auth-state")
      if (!authData) {
        window.location.href = "/"
        return
      }

      const { authToken } = JSON.parse(authData)

      // Create FormData for file upload
      const formData = new FormData()
      const fileInput = document.getElementById("contrato") as HTMLInputElement
      const file = fileInput?.files?.[0]

      if (!file) {
        setError("Por favor, selecione um arquivo PDF.")
        return
      }

      // Validate if real estate was properly selected
      if (!selectedRealEstateId || !isValidRealEstate) {
        setError("Por favor, selecione uma imobiliária da lista de sugestões.")
        return
      }

      // Double-check with backend validation
      const isValid = await validateRealEstate(selectedRealEstateId)
      if (!isValid) {
        setError("Imobiliária selecionada não é válida. Por favor, selecione uma opção da lista.")
        setIsValidRealEstate(false)
        setSelectedRealEstateId("")
        return
      }

      formData.append("contractPdf", file)
      formData.append("rentAmount", data.valorAluguelLiquido.replace(/[^\d]/g, ""))
      formData.append("monthsToAdvance", data.mesesAntecipacao)
      formData.append("realEstateId", selectedRealEstateId)

      const response = await fetch("/api/v1/rental-advance/create", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
        body: formData,
      })

      const result = await response.json()

      if (response.ok) {
        setSuccess("Solicitação criada com sucesso! Redirecionando...")
        setTimeout(() => {
          window.location.href = `/anticipation/validation/${result.id}`
        }, 1500)
      } else {
        setError(result.message || "Erro ao criar solicitação")
      }
    } catch (error) {
      console.error("Error creating anticipation:", error)
      setError("Erro de conexão. Tente novamente.")
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 px-4 pt-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/dashboard"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Voltar para dashboard"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/dashboard"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Fechar"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="px-4">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">Olá, {user?.name?.split(" ")[0]}!</h1>
          <p className="text-white/90 text-sm">Vamos cadastrar os dados da sua operação de antecipação</p>
        </div>

        <Card className="bg-white shadow-xl rounded-2xl border-0 mb-6">
          <CardContent className="space-y-4 p-6">
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                {error}
              </div>
            )}
            {success && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
                {success}
              </div>
            )}

            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <Controller
                name="valorAluguelLiquido"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2">
                    <label htmlFor="valorAluguelLiquido" className="block text-sm font-medium text-gray-700">
                      Valor do Aluguel Líquido (que você recebe) <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="valorAluguelLiquido"
                        placeholder="R$ 0,00"
                        value={field.value}
                        onChange={(e) => {
                          const maskedValue = maskCurrency(e.target.value)
                          field.onChange(maskedValue)
                        }}
                        className={`pl-10 pr-4 ${fieldState.error ? "border-red-500 focus:border-red-500" : ""}`}
                        aria-invalid={!!fieldState.error}
                      />
                    </div>
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <Controller
                name="mesesAntecipacao"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2">
                    <label htmlFor="mesesAntecipacao" className="block text-sm font-medium text-gray-700">
                      Quantos meses deseja antecipar? <span className="text-gray-500">(máx. 12)</span>
                    </label>
                    <div className="relative">
                      <CalendarDays className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="mesesAntecipacao"
                        type="number"
                        min="1"
                        max="12"
                        placeholder="Ex: 6"
                        value={field.value}
                        onChange={(e) => {
                          const value = e.target.value
                          if (value === "" || (Number(value) >= 1 && Number(value) <= 12)) {
                            field.onChange(value)
                          }
                        }}
                        className={`pl-10 pr-4 ${fieldState.error ? "border-red-500 focus:border-red-500" : ""}`}
                        aria-invalid={!!fieldState.error}
                      />
                    </div>
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <Controller
                name="imobiliaria"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2 relative">
                    <label className="block text-sm font-medium text-gray-700">
                      Imobiliária <span className="text-red-500">*</span>
                      {isValidRealEstate && (
                        <span className="ml-2 text-xs text-green-600 font-medium">✓ Selecionada</span>
                      )}
                    </label>
                    <div className="relative">
                      <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      {isValidRealEstate && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      )}
                      <Input
                        ref={inputRef}
                        placeholder="Digite o nome da imobiliária"
                        value={field.value}
                        onChange={(e) => {
                          field.onChange(e.target.value)
                          setSelectedRealEstateId("")
                          setIsValidRealEstate(false)
                          searchRealEstates(e.target.value)
                        }}
                        onFocus={() => {
                          if (field.value && filteredRealEstates.length > 0) {
                            setShowSuggestions(true)
                          }
                        }}
                        onBlur={() => {
                          // Pequeno delay para permitir o clique nas sugestões
                          setTimeout(() => setShowSuggestions(false), 150)
                        }}
                        className={`pl-10 ${isValidRealEstate ? 'pr-12' : 'pr-4'} transition-all duration-200 ${
                          fieldState.error 
                            ? "border-red-500 focus:border-red-500" 
                            : isValidRealEstate 
                              ? "border-green-500 focus:border-green-500 bg-green-50" 
                              : ""
                        }`}
                        aria-invalid={!!fieldState.error}
                        readOnly={isValidRealEstate}
                      />
                      
                      {/* Botão para limpar seleção */}
                      {isValidRealEstate && (
                        <button
                          type="button"
                          onClick={() => {
                            form.setValue("imobiliaria", "")
                            setSelectedRealEstateId("")
                            setIsValidRealEstate(false)
                            inputRef.current?.focus()
                          }}
                          className="absolute right-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                          aria-label="Limpar seleção"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      )}
                      
                      {/* Dropdown de sugestões */}
                      {showSuggestions && !isValidRealEstate && (
                        <div className="absolute z-50 w-full mt-1 bg-white border-2 border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          {filteredRealEstates.length > 0 ? (
                            <>
                              <div className="px-4 py-2 bg-blue-50 border-b border-blue-100 text-xs text-blue-600 font-medium">
                                Selecione uma das opções abaixo:
                              </div>
                              {filteredRealEstates.map((realEstate) => (
                                <button
                                  key={realEstate.id}
                                  type="button"
                                  onClick={() => selectRealEstate(realEstate)}
                                  className="w-full px-4 py-3 text-left text-sm hover:bg-blue-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 flex items-center justify-between group"
                                >
                                  <span>{realEstate.name}</span>
                                  <span className="text-xs text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    Clique para selecionar
                                  </span>
                                </button>
                              ))}
                            </>
                          ) : field.value.trim().length >= 2 ? (
                            <div className="px-4 py-6 text-center">
                              <div className="text-gray-500 mb-3">
                                <span className="text-sm">Imobiliária não encontrada</span>
                              </div>
                              <Button
                                type="button"
                                onClick={openWhatsApp}
                                variant="outline"
                                size="sm"
                                className="text-xs border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white transition-colors duration-200"
                              >
                                <ExternalLink className="w-3 h-3 mr-1" />
                                Solicitar adição via WhatsApp
                              </Button>
                              <p className="text-xs text-gray-400 mt-2">
                                Envie uma mensagem para (85) 99966-6347
                              </p>
                            </div>
                          ) : null}
                        </div>
                      )}
                    </div>
                    {!isValidRealEstate && field.value && (
                      <p className="text-xs text-amber-600 flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        Você deve selecionar uma imobiliária da lista
                      </p>
                    )}
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <Controller
                name="contratoFileName"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Contrato de Locação (PDF) <span className="text-red-500">*</span>
                    </label>
                    <label
                      htmlFor="contrato"
                      className={`flex flex-col items-center justify-center w-full h-16 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${
                        field.value
                          ? "border-blue-500 bg-blue-50 text-blue-600"
                          : "border-gray-300 bg-gray-50 text-gray-500 hover:border-gray-400"
                      } ${fieldState.error ? "border-red-500" : ""}`}
                    >
                      <Upload className={`w-5 h-5 mb-1 ${field.value ? "text-blue-600" : "text-gray-400"}`} />
                      <span className="text-sm font-medium">{field.value || "Clique para fazer upload"}</span>
                      <span className="text-xs text-gray-400">Apenas PDF - até 20MB</span>
                    </label>
                    <Input id="contrato" type="file" accept=".pdf" onChange={handleFileChange} className="hidden" />
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <div className="flex items-start space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <Controller
                  name="dataConsent"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <>
                      <input
                        type="checkbox"
                        id="dataConsent"
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        className="mt-0.5 w-4 h-4"
                      />
                      <div>
                        <label htmlFor="dataConsent" className="text-sm text-gray-700 cursor-pointer">
                          Autorizo o uso dos meus dados para análise da antecipação conforme nossa{" "}
                          <Button variant="link" className="p-0 h-auto text-blue-600 text-sm font-medium underline">
                            Política de Privacidade
                          </Button>
                          .
                        </label>
                        {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                      </div>
                    </>
                  )}
                />
              </div>

              <Button 
                type="submit" 
                className="w-full h-12 text-sm font-semibold bg-gradient-to-r from-[#0B4375] to-blue-700 hover:from-[#0B4375] hover:to-blue-800 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={loading || !isValidRealEstate}
              >
                {loading ? "PROCESSANDO..." : "SOLICITAR ANTECIPAÇÃO"}
                <Zap className="w-4 h-4" />
              </Button>
              
              {!isValidRealEstate && form.watch("imobiliaria") && (
                <p className="text-xs text-center text-amber-600 mt-2 flex items-center justify-center gap-1">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  Selecione uma imobiliária da lista para continuar
                </p>
              )}
            </form>
          </CardContent>
        </Card>

        {/* Security Badge */}
        <div className="flex items-center justify-center gap-2 mt-4 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
          <Lock className="w-4 h-4 text-white/80" />
          <span className="text-sm text-white/80">Seus dados estão protegidos com criptografia de ponta</span>
        </div>
      </div>
    </div>
  )
}
