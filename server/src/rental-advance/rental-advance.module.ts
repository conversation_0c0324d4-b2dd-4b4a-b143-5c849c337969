import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { RentalAdvanceService } from './rental-advance.service';
import { RentalAdvanceController } from './rental-advance.controller';
import { RentalAdvanceMappingService } from './services/rental-advance-mapping.service';
import { RentalAdvanceServiceEnhanced } from './services/rental-advance-enhanced.service';
import { PrismaService } from '../prisma.service';
import { N8nService } from '../integrations/n8n/n8n.service';
import { DriveService } from '../integrations/drive/drive.service';
import { FileUploadService } from '../integrations/file-upload/file-upload.service';
import { CacheService } from '../cache/cache.service';
import { QueueModule } from '../queue/queue.module';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'locpay-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
    QueueModule,
  ],
  controllers: [RentalAdvanceController],
  providers: [
    RentalAdvanceService,
    RentalAdvanceServiceEnhanced,
    RentalAdvanceMappingService,
    PrismaService,
    N8nService,
    DriveService,
    FileUploadService,
    CacheService,
    JwtAuthGuard,
    AdminGuard,
  ],
})
export class RentalAdvanceModule {}
