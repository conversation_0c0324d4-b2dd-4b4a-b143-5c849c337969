import {
  Controller,
  Get,
  Post,
  Body,
  Req,
  UseGuards,
  UploadedFile,
  UseInterceptors,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  HttpException,
  BadRequestException,
  UseFilters,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { RentalAdvanceService } from './rental-advance.service';
import { RentalAdvanceServiceEnhanced } from './services/rental-advance-enhanced.service';
import { RentalAdvanceMappingService } from './services/rental-advance-mapping.service';
import { ApiResponseBuilder, ApiResponse, PaginatedResponse } from '../common/dto/api-response.dto';
import { CreateRentalAdvanceDto } from './dto/create-rental-advance.dto';
import { ConfirmExtractedDataDto } from './dto/confirm-extracted-data.dto';
import { ProposalRequestDto } from './dto/proposal-request.dto';
import { FinalConfirmationDto } from './dto/final-confirmation.dto';
import { ReviewDecisionDto } from './dto/review-decision.dto';
import {
  RentalAdvanceItemDto,
  RentalAdvanceDetailDto,
  CreateRentalAdvanceResponseDto,
  ExtractedDataResponseDto,
  ProposalResponseDto,
  FinalConfirmationResponseDto
} from './dto/rental-advance-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';
import { Admin } from '../common/decorators/admin.decorator';
import { ErrorInterceptor } from '../common/interceptors/error.interceptor';
import {
  StandardRateLimit,
  UploadRateLimit,
  SensitiveRateLimit,
} from '../rate-limit/rate-limit.decorator';

@Controller('rental-advance')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ErrorInterceptor)
@StandardRateLimit()
export class RentalAdvanceController {
  constructor(
    private readonly rentalAdvanceService: RentalAdvanceService,
    private readonly enhancedService: RentalAdvanceServiceEnhanced,
    private readonly mappingService: RentalAdvanceMappingService,
  ) {}

  /**
   * Lista todas as solicitações de antecipação do usuário
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  async listUserRequests(
    @Req() req,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 10,
    @Query('status') status?: string,
  ): Promise<PaginatedResponse<RentalAdvanceItemDto>> {
    try {
      const result = await this.enhancedService.listUserRequestsPaginated(
        req.user.id,
        page,
        limit,
        status,
      );

      const mappedData = result.data.map(item => 
        this.mappingService.toRentalAdvanceItem(item)
      );

      return ApiResponseBuilder.paginated(
        mappedData,
        page,
        limit,
        result.total,
        'Solicitações recuperadas com sucesso'
      );
    } catch (error) {
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao recuperar solicitações',
          'LIST_REQUESTS_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Busca detalhes de uma operação específica
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async getOperation(
    @Param('id') id: string, 
    @Req() req
  ): Promise<ApiResponse<RentalAdvanceDetailDto>> {
    try {
      const operation = await this.rentalAdvanceService.getOperationById(id, req.user.id);
      const mappedData = this.mappingService.toRentalAdvanceDetail(operation);

      return ApiResponseBuilder.success(
        mappedData,
        'Operação recuperada com sucesso'
      );
    } catch (error) {
      if (error.message.includes('não encontrada')) {
        throw new HttpException(
          ApiResponseBuilder.error(
            'Operação não encontrada',
            'OPERATION_NOT_FOUND'
          ),
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao recuperar operação',
          'GET_OPERATION_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Cria uma nova solicitação de antecipação
   */
  @Post('create')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FileInterceptor('contractPdf', {
    fileFilter: (req, file, cb) => {
      if (file.mimetype === 'application/pdf') {
        cb(null, true);
      } else {
        cb(new BadRequestException('Apenas arquivos PDF são aceitos'), false);
      }
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
    },
  }))
  @UploadRateLimit()
  async createRequest(
    @Req() req,
    @Body() dto: CreateRentalAdvanceDto,
    @UploadedFile() contractPdf: Express.Multer.File,
  ): Promise<ApiResponse<CreateRentalAdvanceResponseDto>> {
    try {
      if (!contractPdf) {
        throw new BadRequestException('Arquivo PDF do contrato é obrigatório');
      }

      // Log detalhado do arquivo recebido
      console.log('=== ARQUIVO RECEBIDO ===');
      console.log('Nome original:', contractPdf.originalname);
      console.log('MIME type:', contractPdf.mimetype);
      console.log('Tamanho:', contractPdf.size, 'bytes');
      console.log('Buffer length:', contractPdf.buffer?.length);
      console.log('Has buffer:', !!contractPdf.buffer);

      if (contractPdf.buffer && contractPdf.buffer.length > 0) {
        const header = contractPdf.buffer.subarray(0, 10).toString();
        console.log('Buffer header:', header);
        console.log('É PDF válido:', header.startsWith('%PDF'));
      }
      console.log('========================');

      const result = await this.rentalAdvanceService.createRequest(req.user, dto, contractPdf);
      const responseData = this.mappingService.toCreateResponse(result.operationId, result.jobId);

      return ApiResponseBuilder.success(
        responseData,
        'Solicitação criada com sucesso'
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new HttpException(
          ApiResponseBuilder.error(
            error.message,
            'VALIDATION_ERROR'
          ),
          HttpStatus.BAD_REQUEST
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao criar solicitação',
          'CREATE_REQUEST_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Busca dados extraídos do contrato PDF
   */
  @Get(':id/extracted-data')
  @HttpCode(HttpStatus.OK)
  async getExtractedData(
    @Param('id') operationId: string, 
    @Req() req
  ): Promise<ApiResponse<ExtractedDataResponseDto>> {
    try {
      const result = await this.enhancedService.getExtractedDataFormatted(req.user, operationId);
      
      return ApiResponseBuilder.success(
        result,
        'Dados extraídos recuperados com sucesso'
      );
    } catch (error) {
      if (error.message.includes('não encontrada')) {
        throw new HttpException(
          ApiResponseBuilder.error(
            'Operação não encontrada',
            'OPERATION_NOT_FOUND',
            operationId
          ),
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao recuperar dados extraídos',
          'GET_EXTRACTED_DATA_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Confirma os dados extraídos do contrato
   */
  @Post('confirm-data')
  @HttpCode(HttpStatus.OK)
  @SensitiveRateLimit()
  async confirmExtractedData(
    @Req() req, 
    @Body() dto: ConfirmExtractedDataDto
  ): Promise<ApiResponse<{ operationId: string; status: string }>> {
    try {
      const result = await this.rentalAdvanceService.confirmExtractedData(req.user, dto);

      return ApiResponseBuilder.success(
        {
          operationId: dto.operationId,
          status: 'data_confirmed',
        },
        'Dados confirmados com sucesso. Você pode agora solicitar uma proposta.'
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new HttpException(
          ApiResponseBuilder.error(
            error.message,
            'VALIDATION_ERROR'
          ),
          HttpStatus.BAD_REQUEST
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao confirmar dados',
          'CONFIRM_DATA_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Solicita geração de proposta de crédito
   */
  @Post('request-proposal')
  @HttpCode(HttpStatus.OK)
  @SensitiveRateLimit()
  async requestProposal(
    @Req() req, 
    @Body() dto: ProposalRequestDto
  ): Promise<ApiResponse<{ operationId: string; status: string; estimatedTime: string }>> {
    try {
      const result = await this.rentalAdvanceService.requestProposal(req.user, dto);

      return ApiResponseBuilder.success(
        {
          operationId: dto.operationId,
          status: 'pending_proposal',
          estimatedTime: '10-15 minutos',
        },
        'Proposta solicitada com sucesso. Aguarde a geração da proposta.'
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new HttpException(
          ApiResponseBuilder.error(
            error.message,
            'VALIDATION_ERROR'
          ),
          HttpStatus.BAD_REQUEST
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao solicitar proposta',
          'REQUEST_PROPOSAL_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Busca proposta gerada para a operação
   */
  @Get(':id/proposal')
  @HttpCode(HttpStatus.OK)
  async getProposal(
    @Param('id') operationId: string,
    @Req() req
  ): Promise<ApiResponse<ProposalResponseDto>> {
    try {
      const proposal = await this.enhancedService.getProposal(req.user, operationId);

      return ApiResponseBuilder.success(
        proposal,
        'Proposta recuperada com sucesso'
      );
    } catch (error) {
      if (error.message.includes('não encontrada')) {
        throw new HttpException(
          ApiResponseBuilder.error(
            'Proposta não encontrada ou ainda não gerada',
            'PROPOSAL_NOT_FOUND'
          ),
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao recuperar proposta',
          'GET_PROPOSAL_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Finaliza a solicitação com chave PIX e documento
   */
  @Post('final-confirmation')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('identityDoc', {
    fileFilter: (req, file, cb) => {
      const allowedMimes = ['image/jpeg', 'image/png', 'application/pdf'];
      if (allowedMimes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new BadRequestException('Apenas imagens (JPEG, PNG) ou PDF são aceitos'), false);
      }
    },
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB
    },
  }))
  @UploadRateLimit()
  async finalConfirmation(
    @Req() req,
    @Body() dto: FinalConfirmationDto,
    @UploadedFile() identityDoc: Express.Multer.File,
  ): Promise<ApiResponse<FinalConfirmationResponseDto>> {
    try {
      if (!identityDoc) {
        throw new BadRequestException('Documento de identidade é obrigatório');
      }

      const result = await this.enhancedService.finalConfirmationFormatted(
        req.user,
        dto,
        identityDoc,
      );

      return ApiResponseBuilder.success(
        result,
        'Solicitação finalizada com sucesso! Sua operação será analisada em breve.'
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new HttpException(
          ApiResponseBuilder.error(
            error.message,
            'VALIDATION_ERROR'
          ),
          HttpStatus.BAD_REQUEST
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao finalizar solicitação',
          'FINAL_CONFIRMATION_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Cancela uma operação (apenas em status permitidos)
   */
  @Post(':id/cancel')
  @HttpCode(HttpStatus.OK)
  @SensitiveRateLimit()
  async cancelOperation(
    @Param('id') operationId: string,
    @Req() req,
    @Body() body: { reason?: string }
  ): Promise<ApiResponse<{ operationId: string; status: string }>> {
    try {
      const result = await this.enhancedService.cancelOperation(
        req.user,
        operationId,
        body.reason
      );

      return ApiResponseBuilder.success(
        {
          operationId: result.operationId,
          status: result.status,
        },
        'Operação cancelada com sucesso'
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new HttpException(
          ApiResponseBuilder.error(
            error.message,
            'VALIDATION_ERROR'
          ),
          HttpStatus.BAD_REQUEST
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao cancelar operação',
          'CANCEL_OPERATION_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Endpoint para receber webhooks do N8N (processamento de PDFs, propostas, etc.)
   */
  @Post('webhook/n8n-callback')
  @HttpCode(HttpStatus.OK)
  async handleN8nWebhook(
    @Body() payload: any
  ): Promise<ApiResponse<{ processed: boolean }>> {
    try {
      await this.enhancedService.handleWebhookCallback(payload);

      return ApiResponseBuilder.success(
        { processed: true },
        'Webhook processado com sucesso'
      );
    } catch (error) {
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao processar webhook',
          'WEBHOOK_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // ===== ENDPOINTS ADMIN =====

  /**
   * Lista operações pendentes de revisão (admin)
   */
  @Get('admin/pending-reviews')
  @UseGuards(AdminGuard)
  @Admin()
  @HttpCode(HttpStatus.OK)
  async getPendingReviews(
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
  ): Promise<ApiResponse<any>> {
    try {
      const result = await this.rentalAdvanceService.getOperationsForReview(page, limit);

      return ApiResponseBuilder.success(
        result,
        'Operações pendentes recuperadas com sucesso'
      );
    } catch (error) {
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao recuperar operações pendentes',
          'GET_PENDING_REVIEWS_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Busca operação específica para admin
   */
  @Get('admin/operation/:id')
  @UseGuards(AdminGuard)
  @Admin()
  @HttpCode(HttpStatus.OK)
  async getOperationForAdmin(@Param('id') id: string): Promise<ApiResponse<any>> {
    try {
      const operation = await this.rentalAdvanceService.getOperationById(id);

      return ApiResponseBuilder.success(
        operation,
        'Operação recuperada com sucesso'
      );
    } catch (error) {
      if (error.message.includes('não encontrada')) {
        throw new HttpException(
          ApiResponseBuilder.error(
            'Operação não encontrada',
            'OPERATION_NOT_FOUND'
          ),
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao recuperar operação',
          'GET_OPERATION_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Toma decisão de revisão (admin)
   */
  @Post('admin/review-decision')
  @UseGuards(AdminGuard)
  @Admin()
  @HttpCode(HttpStatus.OK)
  @SensitiveRateLimit()
  async makeReviewDecision(
    @Req() req,
    @Body() dto: ReviewDecisionDto
  ): Promise<ApiResponse<any>> {
    try {
      const result = await this.rentalAdvanceService.reviewDecision(req.user.id, dto);

      return ApiResponseBuilder.success(
        result,
        'Decisão de revisão processada com sucesso'
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new HttpException(
          ApiResponseBuilder.error(
            error.message,
            'VALIDATION_ERROR'
          ),
          HttpStatus.BAD_REQUEST
        );
      }
      throw new HttpException(
        ApiResponseBuilder.error(
          'Erro ao processar decisão de revisão',
          'REVIEW_DECISION_ERROR',
          error.message
        ),
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
