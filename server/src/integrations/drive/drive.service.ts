import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { google, drive_v3 } from 'googleapis';
import { Readable } from 'stream';

@Injectable()
export class DriveService {
  private drive: drive_v3.Drive;
  private rootFolderId: string;
  private readonly logger = new Logger(DriveService.name);

  constructor() {
    try {
      const auth = new google.auth.GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/drive'],
        keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_PATH,
        credentials: process.env.GOOGLE_SERVICE_ACCOUNT_JSON
          ? JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON)
          : undefined,
      });

      this.drive = google.drive({ version: 'v3', auth });

      this.rootFolderId = process.env.DRIVE_ROOT_FOLDER_ID!;
      if (!this.rootFolderId) {
        throw new Error('DRIVE_ROOT_FOLDER_ID não configurado');
      }

      this.logger.log('DriveService inicializado com sucesso');
      
      // Verificar acesso à pasta raiz na inicialização
      this.verifyRootFolderAccess();
    } catch (error) {
      this.logger.error('Erro ao inicializar DriveService:', error);
      throw error;
    }
  }

  private async getOrCreateFolder(operationId: string): Promise<string> {
    try {
      this.logger.log(`Verificando pasta para operação ${operationId}`);

      // Verificar acesso à pasta raiz antes de prosseguir
      const hasRootAccess = await this.checkFileAccess(this.rootFolderId, 'pasta raiz');
      if (!hasRootAccess) {
        throw new Error(`Sem acesso à pasta raiz ${this.rootFolderId}. Verifique se a pasta foi compartilhada com a conta de serviço.`);
      }

      // Busca pasta existente
      const searchResponse = await this.drive.files.list({
        q: `"${this.rootFolderId}" in parents and name = "${operationId}" and mimeType = 'application/vnd.google-apps.folder' and trashed = false`,
        fields: 'files(id,name)',
      });

      const files = searchResponse.data.files ?? [];
      if (files.length > 0) {
        this.logger.log(
          `Pasta existente encontrada para operação ${operationId}`,
        );
        return files[0].id!;
      }

      // Cria nova pasta
      this.logger.log(`Criando nova pasta para operação ${operationId}`);
      const metadata: drive_v3.Schema$File = {
        name: operationId,
        mimeType: 'application/vnd.google-apps.folder',
        parents: [this.rootFolderId],
      };

      const createResponse = await this.drive.files.create({
        requestBody: metadata,
        fields: 'id',
      });

      const folderId = createResponse.data.id!;
      this.logger.log(
        `Pasta criada com sucesso: ${folderId} para operação ${operationId}`,
      );

      return folderId;
    } catch (error) {
      this.logger.error(`Falha ao obter/criar pasta ${operationId}:`, error);
      
      // Adicionar informações mais específicas sobre o erro
      if (error.code === 404) {
        throw new InternalServerErrorException(
          `Pasta raiz não encontrada (${this.rootFolderId}). Verifique se a pasta foi compartilhada com a conta de serviço.`
        );
      } else if (error.code === 403) {
        throw new InternalServerErrorException(
          'Sem permissão para acessar o Google Drive. Verifique as credenciais da conta de serviço.'
        );
      }
      
      throw new InternalServerErrorException(
        'Erro no Drive ao criar pasta da operação',
      );
    }
  }

  // Método removido - a pasta é criada automaticamente quando necessário

  async saveContractPdf(
    operationId: string,
    contractPdf: Express.Multer.File,
  ): Promise<string> {
    try {
      // Validar se o arquivo tem conteúdo
      if (!contractPdf || !contractPdf.buffer || contractPdf.buffer.length === 0) {
        throw new Error('Arquivo PDF inválido ou vazio');
      }

      this.logger.log(`Salvando PDF de contrato para operação ${operationId} (${contractPdf.buffer.length} bytes)`);

      const folderId = await this.getOrCreateFolder(operationId);

      const fileName = `contrato-${Date.now()}.pdf`;
      const metadata: drive_v3.Schema$File = {
        name: fileName,
        parents: [folderId],
      };

      // Converte buffer para stream de forma mais robusta
      const bufferStream = new Readable();
      bufferStream.push(contractPdf.buffer);
      bufferStream.push(null);

      this.logger.log(`Iniciando upload do arquivo ${fileName} para pasta ${folderId}`);

      const uploadResponse = await this.drive.files.create({
        requestBody: metadata,
        media: {
          mimeType: 'application/pdf',
          body: bufferStream,
        },
        fields: 'id',
      });

      const fileId = uploadResponse.data.id!;
      this.logger.log(`Arquivo criado com ID: ${fileId}`);

      // Define permissões de leitura
      await this.drive.permissions.create({
        fileId,
        requestBody: {
          role: 'reader',
          type: 'anyone',
        },
      });

      // Busca links do arquivo
      const fileInfo = await this.drive.files.get({
        fileId,
        fields: 'webViewLink,webContentLink,size',
      });

      const fileUrl =
        fileInfo.data.webContentLink || fileInfo.data.webViewLink!;

      this.logger.log(
        `PDF de contrato salvo com sucesso para operação ${operationId}: ${fileId}, tamanho: ${fileInfo.data.size} bytes`,
      );
      return fileUrl;
    } catch (error) {
      this.logger.error(
        `Erro ao salvar contrato no Drive para ${operationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Falha ao salvar contrato no Drive',
      );
    }
  }

  async saveIdentityDoc(
    operationId: string,
    identityDoc: Express.Multer.File,
  ): Promise<string> {
    try {
      if (!identityDoc?.buffer || identityDoc.buffer.length === 0) {
        throw new Error('Documento de identidade inválido ou vazio');
      }

      this.logger.log(
        `Salvando documento de identidade para operação ${operationId} (${identityDoc.buffer.length} bytes)`,
      );

      const folderId = await this.getOrCreateFolder(operationId);

      const fileName = `identidade-${Date.now()}-${identityDoc.originalname}`;
      const metadata: drive_v3.Schema$File = {
        name: fileName,
        parents: [folderId],
      };

      // Converte buffer para stream
      const bufferStream = new Readable();
      bufferStream.push(identityDoc.buffer);
      bufferStream.push(null);

      const uploadResponse = await this.drive.files.create({
        requestBody: metadata,
        media: {
          mimeType: identityDoc.mimetype,
          body: bufferStream,
        },
        fields: 'id',
      });

      const fileId = uploadResponse.data.id!;

      // Define permissões de leitura
      await this.drive.permissions.create({
        fileId,
        requestBody: {
          role: 'reader',
          type: 'anyone',
        },
      });

      // Busca links do arquivo
      const fileInfo = await this.drive.files.get({
        fileId,
        fields: 'webViewLink,webContentLink',
      });

      const fileUrl =
        fileInfo.data.webContentLink || fileInfo.data.webViewLink!;

      this.logger.log(
        `Documento de identidade salvo com sucesso para operação ${operationId}: ${fileId}`,
      );
      return fileUrl;
    } catch (error) {
      this.logger.error(
        `Erro ao salvar documento de identidade no Drive para ${operationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Falha ao salvar documento de identidade no Drive',
      );
    }
  }

  async listOperationFiles(operationId: string): Promise<any[]> {
    try {
      const folderId = await this.getOrCreateFolder(operationId);

      const response = await this.drive.files.list({
        q: `"${folderId}" in parents and trashed = false`,
        fields: 'files(id,name,mimeType,createdTime,size,webViewLink)',
        orderBy: 'createdTime desc',
      });

      return response.data.files ?? [];
    } catch (error) {
      this.logger.error(
        `Erro ao listar arquivos da operação ${operationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Erro ao listar arquivos da operação',
      );
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      await this.drive.files.delete({ fileId });
      this.logger.log(`Arquivo deletado com sucesso: ${fileId}`);
    } catch (error) {
      this.logger.error(`Erro ao deletar arquivo ${fileId}:`, error);
      throw new InternalServerErrorException('Erro ao deletar arquivo');
    }
  }

  private async verifyRootFolderAccess(): Promise<void> {
    try {
      this.logger.log(`Verificando acesso à pasta raiz: ${this.rootFolderId}`);
      
      const response = await this.drive.files.get({
        fileId: this.rootFolderId,
        fields: 'id,name,mimeType,owners,permissions',
      });

      if (response.data.mimeType !== 'application/vnd.google-apps.folder') {
        throw new Error('O ID fornecido não é de uma pasta');
      }

      this.logger.log(`Acesso à pasta raiz confirmado: ${response.data.name}`);
      
      // Log informações sobre permissões se disponíveis
      if (response.data.permissions) {
        this.logger.log(`Permissões da pasta: ${response.data.permissions.length} item(s)`);
      }
      
    } catch (error) {
      this.logger.error('Erro ao verificar acesso à pasta raiz:', error);
      
      if (error.code === 404) {
        this.logger.error(`A pasta com ID ${this.rootFolderId} não foi encontrada ou a conta de serviço não tem acesso a ela.`);
        this.logger.error('Certifique-se de que:');
        this.logger.error('1. O ID da pasta está correto');
        this.logger.error('2. A pasta foi compartilhada com o email da conta de serviço');
        this.logger.error('3. A conta de serviço tem permissões adequadas');
      }
      
      // Não lança o erro para não quebrar a inicialização, apenas loga
      // O erro será tratado quando tentar usar os métodos
    }
  }

  private async checkFileAccess(fileId: string, operation: string): Promise<boolean> {
    try {
      await this.drive.files.get({
        fileId,
        fields: 'id',
      });
      return true;
    } catch (error) {
      this.logger.error(`Erro ao verificar acesso para ${operation} (ID: ${fileId}):`, error);
      
      if (error.code === 404) {
        this.logger.error(`Arquivo/pasta não encontrado ou sem acesso: ${fileId}`);
        this.logger.error('Verifique se o arquivo existe e se a conta de serviço tem acesso.');
      }
      
      return false;
    }
  }

  async getServiceAccountInfo(): Promise<any> {
    try {
      const response = await this.drive.about.get({
        fields: 'user,storageQuota',
      });
      
      this.logger.log('Informações da conta de serviço:', {
        email: response.data.user?.emailAddress,
        displayName: response.data.user?.displayName,
        quota: response.data.storageQuota,
      });
      
      return response.data;
    } catch (error) {
      this.logger.error('Erro ao obter informações da conta de serviço:', error);
      throw error;
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      this.logger.log('Testando conexão com Google Drive...');
      
      // Teste básico de conexão
      await this.drive.about.get({ fields: 'user' });
      
      // Teste de acesso à pasta raiz
      const hasRootAccess = await this.checkFileAccess(this.rootFolderId, 'teste de conexão');
      
      if (hasRootAccess) {
        this.logger.log('Conexão com Google Drive OK');
        return true;
      } else {
        this.logger.error('Conexão estabelecida mas sem acesso à pasta raiz');
        return false;
      }
    } catch (error) {
      this.logger.error('Falha no teste de conexão:', error);
      return false;
    }
  }
}
