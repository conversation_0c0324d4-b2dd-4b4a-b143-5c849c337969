import { Controller, Get, Logger } from '@nestjs/common';
import { DriveService } from './drive.service';

@Controller('drive')
export class DriveController {
  private readonly logger = new Logger(DriveController.name);

  constructor(private readonly driveService: DriveService) {}

  @Get('test-connection')
  async testConnection() {
    try {
      this.logger.log('Iniciando teste de conexão do Google Drive...');
      
      const isConnected = await this.driveService.testConnection();
      const serviceAccountInfo = await this.driveService.getServiceAccountInfo();
      
      return {
        success: isConnected,
        message: isConnected ? 'Conexão com Google Drive OK' : 'Falha na conexão',
        serviceAccount: {
          email: serviceAccountInfo.user?.emailAddress,
          displayName: serviceAccountInfo.user?.displayName,
        },
        rootFolderId: process.env.DRIVE_ROOT_FOLDER_ID,
      };
    } catch (error) {
      this.logger.error('Erro no teste de conexão:', error);
      return {
        success: false,
        message: 'Erro no teste de conexão',
        error: error.message,
        rootFolderId: process.env.DRIVE_ROOT_FOLDER_ID,
      };
    }
  }

  @Get('service-account-info')
  async getServiceAccountInfo() {
    try {
      const info = await this.driveService.getServiceAccountInfo();
      return {
        success: true,
        data: info,
      };
    } catch (error) {
      this.logger.error('Erro ao obter informações da conta de serviço:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
