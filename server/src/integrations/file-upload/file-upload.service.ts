import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { google, drive_v3 } from 'googleapis';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);

export interface UploadResult {
  fileId: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
}

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);
  private drive: drive_v3.Drive;
  private rootFolderId: string;

  constructor() {
    try {
      const auth = new google.auth.GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/drive'],
        keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_PATH,
        credentials: process.env.GOOGLE_SERVICE_ACCOUNT_JSON
          ? JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON)
          : undefined,
      });

      this.drive = google.drive({ version: 'v3', auth });
      this.rootFolderId = process.env.DRIVE_ROOT_FOLDER_ID!;

      if (!this.rootFolderId) {
        throw new Error('DRIVE_ROOT_FOLDER_ID não configurado');
      }

      this.logger.log('FileUploadService inicializado com sucesso');
    } catch (error) {
      this.logger.error('Erro ao inicializar FileUploadService:', error);
      throw error;
    }
  }

  /**
   * Valida se o arquivo é um PDF válido
   */
  private validatePdfFile(file: Express.Multer.File): void {
    this.logger.log('=== VALIDANDO ARQUIVO PDF ===');

    if (!file) {
      this.logger.error('Nenhum arquivo foi enviado');
      throw new BadRequestException('Nenhum arquivo foi enviado');
    }

    this.logger.log(`Arquivo recebido: ${file.originalname}`);
    this.logger.log(`MIME type: ${file.mimetype}`);
    this.logger.log(`Tamanho: ${file.size} bytes`);

    if (!file.buffer || file.buffer.length === 0) {
      this.logger.error('Buffer do arquivo está vazio');
      throw new BadRequestException('Arquivo está vazio');
    }

    this.logger.log(`Buffer length: ${file.buffer.length} bytes`);

    if (file.mimetype !== 'application/pdf') {
      this.logger.error(`MIME type inválido: ${file.mimetype}`);
      throw new BadRequestException('Apenas arquivos PDF são aceitos');
    }

    // Verificar se o arquivo realmente é um PDF verificando o header
    const pdfHeader = file.buffer.subarray(0, 4).toString();
    this.logger.log(`PDF header: "${pdfHeader}"`);

    if (pdfHeader !== '%PDF') {
      this.logger.error(`Header inválido: "${pdfHeader}" (esperado: "%PDF")`);
      throw new BadRequestException('Arquivo não é um PDF válido');
    }

    // Verificar tamanho máximo (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      this.logger.error(`Arquivo muito grande: ${file.size} bytes (máximo: ${maxSize})`);
      throw new BadRequestException('Arquivo muito grande. Máximo 10MB');
    }

    this.logger.log(`✅ PDF válido: ${file.originalname}, ${file.size} bytes`);
    this.logger.log('==============================');
  }

  /**
   * Cria ou obtém uma pasta no Google Drive
   */
  private async getOrCreateFolder(folderName: string): Promise<string> {
    try {
      // Buscar pasta existente
      const searchResponse = await this.drive.files.list({
        q: `"${this.rootFolderId}" in parents and name = "${folderName}" and mimeType = 'application/vnd.google-apps.folder' and trashed = false`,
        fields: 'files(id,name)',
      });

      const folders = searchResponse.data.files || [];
      
      if (folders.length > 0) {
        this.logger.log(`Pasta existente encontrada: ${folderName}`);
        return folders[0].id!;
      }

      // Criar nova pasta
      this.logger.log(`Criando nova pasta: ${folderName}`);
      const createResponse = await this.drive.files.create({
        requestBody: {
          name: folderName,
          mimeType: 'application/vnd.google-apps.folder',
          parents: [this.rootFolderId],
        },
        fields: 'id',
      });

      const folderId = createResponse.data.id!;
      this.logger.log(`Pasta criada com sucesso: ${folderId}`);
      return folderId;
    } catch (error) {
      this.logger.error(`Erro ao criar/obter pasta ${folderName}:`, error);
      throw new InternalServerErrorException('Erro ao criar pasta no Google Drive');
    }
  }

  /**
   * Salva arquivo temporariamente no sistema de arquivos
   */
  private async saveTemporaryFile(
    file: Express.Multer.File,
    operationId: string,
  ): Promise<string> {
    const tempDir = '/tmp';
    const fileName = `${operationId}-${Date.now()}-${file.originalname}`;
    const tempPath = path.join(tempDir, fileName);

    try {
      await writeFile(tempPath, file.buffer);
      this.logger.log(`Arquivo temporário salvo: ${tempPath}`);
      return tempPath;
    } catch (error) {
      this.logger.error('Erro ao salvar arquivo temporário:', error);
      throw new InternalServerErrorException('Erro ao processar arquivo');
    }
  }

  /**
   * Faz upload do arquivo para o Google Drive usando arquivo temporário
   */
  private async uploadToGoogleDrive(
    tempFilePath: string,
    fileName: string,
    folderId: string,
    mimeType: string,
  ): Promise<UploadResult> {
    try {
      this.logger.log('=== UPLOAD PARA GOOGLE DRIVE ===');
      this.logger.log(`Arquivo: ${fileName}`);
      this.logger.log(`Pasta ID: ${folderId}`);
      this.logger.log(`MIME type: ${mimeType}`);
      this.logger.log(`Arquivo temporário: ${tempFilePath}`);

      // Verificar se o arquivo temporário existe e tem conteúdo
      const stats = fs.statSync(tempFilePath);
      this.logger.log(`Tamanho do arquivo temporário: ${stats.size} bytes`);

      if (stats.size === 0) {
        throw new Error('Arquivo temporário está vazio');
      }

      // Criar stream do arquivo temporário
      const fileStream = fs.createReadStream(tempFilePath);

      this.logger.log('Iniciando upload...');
      const uploadResponse = await this.drive.files.create({
        requestBody: {
          name: fileName,
          parents: [folderId],
        },
        media: {
          mimeType,
          body: fileStream,
        },
        fields: 'id,size',
      });

      const fileId = uploadResponse.data.id!;
      const fileSize = parseInt(uploadResponse.data.size || '0');
      this.logger.log(`✅ Arquivo criado no Drive: ${fileId}`);
      this.logger.log(`Tamanho no Drive: ${fileSize} bytes`);

      // Definir permissões públicas
      this.logger.log('Definindo permissões...');
      await this.drive.permissions.create({
        fileId,
        requestBody: {
          role: 'reader',
          type: 'anyone',
        },
      });

      // Obter URL do arquivo
      this.logger.log('Obtendo URL do arquivo...');
      const fileInfo = await this.drive.files.get({
        fileId,
        fields: 'webViewLink,webContentLink',
      });

      const fileUrl = fileInfo.data.webContentLink || fileInfo.data.webViewLink!;
      this.logger.log(`✅ URL do arquivo: ${fileUrl}`);
      this.logger.log('================================');

      return {
        fileId,
        fileName,
        fileUrl,
        fileSize,
      };
    } catch (error) {
      this.logger.error('❌ Erro no upload para Google Drive:', error);
      throw new InternalServerErrorException('Erro ao fazer upload do arquivo');
    }
  }

  /**
   * Método principal para upload de PDF de contrato
   */
  async uploadContractPdf(
    operationId: string,
    file: Express.Multer.File,
  ): Promise<UploadResult> {
    let tempFilePath: string | null = null;

    try {
      // 1. Validar arquivo
      this.validatePdfFile(file);

      // 2. Criar/obter pasta
      const folderId = await this.getOrCreateFolder(operationId);

      // 3. Salvar arquivo temporariamente
      tempFilePath = await this.saveTemporaryFile(file, operationId);

      // 4. Fazer upload para Google Drive
      const fileName = `contrato-${Date.now()}.pdf`;
      const result = await this.uploadToGoogleDrive(
        tempFilePath,
        fileName,
        folderId,
        'application/pdf',
      );

      this.logger.log(`PDF de contrato salvo com sucesso para operação ${operationId}`);
      return result;
    } catch (error) {
      this.logger.error(`Erro ao salvar PDF de contrato para ${operationId}:`, error);
      throw error;
    } finally {
      // Limpar arquivo temporário
      if (tempFilePath) {
        try {
          await unlink(tempFilePath);
          this.logger.log(`Arquivo temporário removido: ${tempFilePath}`);
        } catch (error) {
          this.logger.warn(`Erro ao remover arquivo temporário: ${error.message}`);
        }
      }
    }
  }

  /**
   * Método para upload de documento de identidade
   */
  async uploadIdentityDocument(
    operationId: string,
    file: Express.Multer.File,
  ): Promise<UploadResult> {
    let tempFilePath: string | null = null;

    try {
      // Validar arquivo (aceita PDF, JPEG, PNG)
      if (!file || !file.buffer || file.buffer.length === 0) {
        throw new BadRequestException('Arquivo está vazio');
      }

      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
      if (!allowedTypes.includes(file.mimetype)) {
        throw new BadRequestException('Apenas PDF, JPEG ou PNG são aceitos');
      }

      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new BadRequestException('Arquivo muito grande. Máximo 5MB');
      }

      // Criar/obter pasta
      const folderId = await this.getOrCreateFolder(operationId);

      // Salvar arquivo temporariamente
      tempFilePath = await this.saveTemporaryFile(file, operationId);

      // Fazer upload para Google Drive
      const fileName = `identidade-${Date.now()}-${file.originalname}`;
      const result = await this.uploadToGoogleDrive(
        tempFilePath,
        fileName,
        folderId,
        file.mimetype,
      );

      this.logger.log(`Documento de identidade salvo com sucesso para operação ${operationId}`);
      return result;
    } catch (error) {
      this.logger.error(`Erro ao salvar documento de identidade para ${operationId}:`, error);
      throw error;
    } finally {
      // Limpar arquivo temporário
      if (tempFilePath) {
        try {
          await unlink(tempFilePath);
          this.logger.log(`Arquivo temporário removido: ${tempFilePath}`);
        } catch (error) {
          this.logger.warn(`Erro ao remover arquivo temporário: ${error.message}`);
        }
      }
    }
  }
}
