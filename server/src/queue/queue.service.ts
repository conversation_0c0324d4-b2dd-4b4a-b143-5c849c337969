import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import Redis from 'ioredis';
import { PrismaService } from '../prisma.service';

export interface N8nJobData {
  jobId: string;
  type: 'pdf_extraction' | 'proposal_generation' | 'whatsapp_send';
  relatedEntityId: string;
  entityType: 'rental_request';
  requestData: any;
}

export interface N8nResponse {
  jobId: string;
  success: boolean;
  data?: any;
  error?: string;
}

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);
  private readonly redis: Redis;

  constructor(
    @InjectQueue('n8n-responses') private n8nQueue: Queue,
    private prisma: PrismaService,
  ) {
    // Conexão Redis para comunicação com N8N
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      username: process.env.REDIS_USERNAME,
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_N8N_DB || '4'), // DB dedicado para N8N
    });
  }

  /**
   * Cria um job na queue e salva no banco de dados
   */
  async createJob(jobData: Omit<N8nJobData, 'jobId'>): Promise<string> {
    const jobId = `${jobData.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Salvar no banco de dados
      await this.prisma.n8nJobQueue.create({
        data: {
          jobId,
          type: jobData.type,
          status: 'pending',
          requestData: jobData.requestData,
          relatedEntityId: jobData.relatedEntityId,
          entityType: jobData.entityType,
        },
      });

      // Adicionar à queue do Redis para monitoramento
      await this.n8nQueue.add(
        'monitor-job',
        { jobId },
        {
          delay: 5000, // Começar a monitorar após 5 segundos
          attempts: 10,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      );

      this.logger.log(`Job criado: ${jobId} (${jobData.type})`);
      return jobId;
    } catch (error) {
      this.logger.error(`Erro ao criar job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Processa uma resposta recebida do N8N via Redis
   */
  async processN8nResponse(response: N8nResponse): Promise<void> {
    try {
      const job = await this.prisma.n8nJobQueue.findUnique({
        where: { jobId: response.jobId },
      });

      if (!job) {
        this.logger.warn(`Job não encontrado: ${response.jobId}`);
        return;
      }

      // Atualizar status do job
      await this.prisma.n8nJobQueue.update({
        where: { jobId: response.jobId },
        data: {
          status: response.success ? 'completed' : 'failed',
          responseData: response.data || null,
          errorMessage: response.error || null,
          processedAt: new Date(),
        },
      });

      // Processar dados específicos por tipo de job
      if (response.success) {
        await this.handleSuccessfulJob(job, response.data);
      } else {
        await this.handleFailedJob(job, response.error || 'Erro desconhecido');
      }

      this.logger.log(
        `Resposta processada para job ${response.jobId}: ${response.success ? 'sucesso' : 'falha'}`,
      );
    } catch (error) {
      this.logger.error(
        `Erro ao processar resposta do job ${response.jobId}:`,
        error,
      );
    }
  }

  /**
   * Processa job que foi concluído com sucesso
   */
  private async handleSuccessfulJob(
    job: any,
    responseData: any,
  ): Promise<void> {
    switch (job.type) {
      case 'pdf_extraction':
        await this.handlePdfExtractionSuccess(
          job.relatedEntityId,
          responseData,
        );
        break;
      case 'proposal_generation':
        await this.handleProposalGenerationSuccess(
          job.relatedEntityId,
          responseData,
        );
        break;
      // whatsapp_send não precisa de processamento adicional
    }
  }

  /**
   * Processa extração de PDF bem-sucedida
   */
  private async handlePdfExtractionSuccess(
    rentalRequestId: string,
    extractedData: any,
  ): Promise<void> {
    try {
      // Criar ou atualizar dados do contrato
      await this.prisma.rentalContractData.upsert({
        where: { rentalRequestId },
        create: {
          rentalRequestId,
          propertyAddress: extractedData.propertyAddress,
          landlordName: extractedData.landlordName,
          tenantName: extractedData.tenantName,
          landlordDocument: extractedData.landlordDocument,
          tenantDocument: extractedData.tenantDocument,
          rentalGuarantee: extractedData.rentalGuarantee,
          contractTerm: extractedData.contractTerm,
          startDate: extractedData.startDate
            ? new Date(extractedData.startDate)
            : null,
          endDate: extractedData.endDate
            ? new Date(extractedData.endDate)
            : null,
          propertyRegistry: extractedData.propertyRegistry,
          extractedData: extractedData, // Backup dos dados brutos
        },
        update: {
          propertyAddress: extractedData.propertyAddress,
          landlordName: extractedData.landlordName,
          tenantName: extractedData.tenantName,
          landlordDocument: extractedData.landlordDocument,
          tenantDocument: extractedData.tenantDocument,
          rentalGuarantee: extractedData.rentalGuarantee,
          contractTerm: extractedData.contractTerm,
          startDate: extractedData.startDate
            ? new Date(extractedData.startDate)
            : null,
          endDate: extractedData.endDate
            ? new Date(extractedData.endDate)
            : null,
          propertyRegistry: extractedData.propertyRegistry,
          extractedData: extractedData,
          updatedAt: new Date(),
        },
      });

      // Atualizar status da solicitação
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: rentalRequestId },
        data: { currentStatus: 'pdf_extracted' },
      });

      // Adicionar log de status
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId,
          status: 'pdf_extracted',
        },
      });

      this.logger.log(
        `Dados do contrato salvos para solicitação ${rentalRequestId}`,
      );
    } catch (error) {
      this.logger.error(`Erro ao salvar dados extraídos do PDF:`, error);
    }
  }

  /**
   * Processa geração de proposta bem-sucedida
   */
  private async handleProposalGenerationSuccess(
    rentalRequestId: string,
    proposalData: any,
  ): Promise<void> {
    try {
      // Atualizar dados da proposta
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: rentalRequestId },
        data: {
          currentStatus: 'proposal_sent',
          proposalAmount: proposalData.proposalAmount,
          monthlyRentOffer: proposalData.monthlyRentOffer,
          proposedMonths: proposalData.proposedMonths,
        },
      });

      // Adicionar log de status
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId,
          status: 'proposal_sent',
        },
      });

      this.logger.log(`Proposta salva para solicitação ${rentalRequestId}`);
    } catch (error) {
      this.logger.error(`Erro ao salvar proposta:`, error);
    }
  }

  /**
   * Processa job que falhou
   */
  private async handleFailedJob(job: any, error: string): Promise<void> {
    // Incrementar contador de retry se ainda há tentativas
    if (job.retryCount < job.maxRetries) {
      await this.prisma.n8nJobQueue.update({
        where: { jobId: job.jobId },
        data: {
          status: 'pending',
          retryCount: job.retryCount + 1,
          errorMessage: error,
        },
      });

      this.logger.warn(
        `Job ${job.jobId} será reprocessado (tentativa ${job.retryCount + 1})`,
      );
    } else {
      this.logger.error(`Job ${job.jobId} falhou definitivamente: ${error}`);
    }
  }

  /**
   * Busca jobs pendentes para verificar timeout
   */
  async checkPendingJobs(): Promise<void> {
    const timeoutMinutes = 10; // Jobs que ficam pendentes por mais de 10 minutos
    const timeoutDate = new Date(Date.now() - timeoutMinutes * 60 * 1000);

    const timeoutJobs = await this.prisma.n8nJobQueue.findMany({
      where: {
        status: 'pending',
        createdAt: { lt: timeoutDate },
      },
    });

    for (const job of timeoutJobs) {
      await this.handleFailedJob(
        job,
        'Timeout - Job não processado em tempo hábil',
      );
    }

    if (timeoutJobs.length > 0) {
      this.logger.warn(`${timeoutJobs.length} jobs marcados como timeout`);
    }
  }

  /**
   * Busca job por ID
   */
  async getJob(jobId: string) {
    return this.prisma.n8nJobQueue.findUnique({
      where: { jobId },
    });
  }

  /**
   * Lista jobs por status
   */
  async getJobsByStatus(status: string) {
    return this.prisma.n8nJobQueue.findMany({
      where: { status },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Enviar job para N8N via Redis
   */
  async sendJobToN8n(jobData: N8nJobData): Promise<void> {
    const requestQueueKey = `n8n:requests:${jobData.type}`;

    try {
      await this.redis.lpush(requestQueueKey, JSON.stringify(jobData));
      this.logger.log(`Job ${jobData.jobId} enviado para N8N via Redis`);
    } catch (error) {
      this.logger.error(`Erro ao enviar job para N8N:`, error);
      throw error;
    }
  }

  /**
   * Criar e enviar job para N8N
   */
  async createAndSendJob(jobData: Omit<N8nJobData, 'jobId'>): Promise<string> {
    const jobId = await this.createJob(jobData);

    // Enviar para N8N via Redis
    await this.sendJobToN8n({ ...jobData, jobId });

    return jobId;
  }

  /**
   * Obter estatísticas da queue
   */
  async getQueueStats() {
    const [pending, processing, completed, failed] = await Promise.all([
      this.prisma.n8nJobQueue.count({ where: { status: 'pending' } }),
      this.prisma.n8nJobQueue.count({ where: { status: 'processing' } }),
      this.prisma.n8nJobQueue.count({ where: { status: 'completed' } }),
      this.prisma.n8nJobQueue.count({ where: { status: 'failed' } }),
    ]);

    return { pending, processing, completed, failed };
  }
}
