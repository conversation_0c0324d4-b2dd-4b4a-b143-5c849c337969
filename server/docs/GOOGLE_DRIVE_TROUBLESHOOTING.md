# Diagnóstico e Solução - Google Drive API

## Problema Identificado

Você está recebendo o erro `File not found: 1up1bdqb4dY6qkwXU46-_BADshHIpy1AV` mesmo que a pasta exista e seja acessível pelo navegador. Isso indica um problema de **permissões da conta de serviço**.

## Causa do Problema

A conta de serviço do Google não tem acesso à pasta especificada. Quando você acessa pelo navegador, está usando sua conta pessoal/organizacional, mas a aplicação usa uma conta de serviço diferente.

## Soluções

### 1. Compartilhar a Pasta com a Conta de Serviço

#### Passo 1: Descobrir o email da conta de serviço

Execute o script de teste que criamos:

```bash
cd server
npm install dotenv  # Se não estiver instalado
npm run test:drive
```

Este script irá mostrar o email da conta de serviço e outros detalhes importantes.

#### Passo 2: Compartilhar a pasta

1. Acesse o Google Drive no navegador
2. Navegue até a pasta com ID `1up1bdqb4dY6qkwXU46-_BADshHIpy1AV`
3. Clique com o botão direito na pasta e selecione "Compartilhar"
4. Adicione o **email da conta de serviço** (obtido no Passo 1)
5. Dê permissões de **"Editor"** ou **"Proprietário"**
6. Clique em "Enviar"

### 2. Verificar as Credenciais

Certifique-se de que as variáveis de ambiente estão configuradas corretamente:

```bash
# No seu arquivo .env
DRIVE_ROOT_FOLDER_ID="1up1bdqb4dY6qkwXU46-_BADshHIpy1AV"
GOOGLE_SERVICE_ACCOUNT_JSON='{seu_json_de_credenciais}'
# OU
GOOGLE_SERVICE_ACCOUNT_PATH="/caminho/para/service-account.json"
```

### 3. Testar a Conexão

#### Via Script de Linha de Comando
```bash
npm run test:drive
```

#### Via API HTTP (se o servidor estiver rodando)
```bash
# Teste geral de conexão
curl http://localhost:3000/drive/test-connection

# Informações da conta de serviço
curl http://localhost:3000/drive/service-account-info
```

## Melhorias Implementadas

### 1. DriveService Aprimorado

- ✅ Verificação de acesso à pasta raiz na inicialização
- ✅ Melhor tratamento de erros com mensagens específicas
- ✅ Logs detalhados para diagnóstico
- ✅ Métodos de teste de conectividade

### 2. Novos Endpoints para Diagnóstico

- `GET /drive/test-connection` - Testa a conexão completa
- `GET /drive/service-account-info` - Mostra informações da conta de serviço

### 3. Script de Teste Independente

O arquivo `scripts/test-drive-connection.ts` executa testes completos:

- ✅ Autenticação da conta de serviço
- ✅ Acesso à pasta raiz
- ✅ Listagem de arquivos
- ✅ Criação e remoção de pasta de teste

## Verificação Final

Após implementar as soluções:

1. **Execute o teste**: `npm run test:drive`
2. **Verifique os logs**: Procure por mensagens de sucesso
3. **Teste a aplicação**: Tente usar as funcionalidades que utilizam o Drive

## Dicas Importantes

### Emails da Conta de Serviço
- Geralmente têm formato: `<EMAIL>`
- Podem ser encontrados no Google Cloud Console ou no arquivo JSON de credenciais

### Permissões Necessárias
- **Mínimo**: Editor (para criar/modificar arquivos)
- **Recomendado**: Proprietário (para gerenciar permissões)

### Problemas Comuns
- ❌ Pasta não compartilhada com a conta de serviço
- ❌ Permissões insuficientes (apenas "Visualizador")
- ❌ Credenciais inválidas ou expiradas
- ❌ API do Google Drive não habilitada no projeto

## Próximos Passos

1. Execute `npm run test:drive` para descobrir o email da conta de serviço
2. Compartilhe a pasta `1up1bdqb4dY6qkwXU46-_BADshHIpy1AV` com esse email
3. Dê permissões de "Editor" ou "Proprietário"
4. Execute o teste novamente para confirmar que funcionou
5. Teste a aplicação normalmente

Se ainda tiver problemas, verifique os logs detalhados que foram implementados no DriveService.
