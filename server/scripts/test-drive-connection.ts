import { google } from 'googleapis';
import * as dotenv from 'dotenv';

// Carrega variáveis de ambiente
dotenv.config();

async function testGoogleDriveConnection() {
  console.log('🔍 Testando conexão com Google Drive...\n');

  try {
    // Configurar autenticação
    const auth = new google.auth.GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/drive'],
      keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_PATH,
      credentials: process.env.GOOGLE_SERVICE_ACCOUNT_JSON
        ? JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON)
        : undefined,
    });

    const drive = google.drive({ version: 'v3', auth });
    const rootFolderId = process.env.DRIVE_ROOT_FOLDER_ID;

    console.log(`📁 Pasta raiz configurada: ${rootFolderId}\n`);

    // Teste 1: Informações da conta de serviço
    console.log('📋 Teste 1: Informações da conta de serviço');
    try {
      const aboutResponse = await drive.about.get({
        fields: 'user,storageQuota',
      });

      console.log(`✅ Email da conta: ${aboutResponse.data.user?.emailAddress}`);
      console.log(`✅ Nome: ${aboutResponse.data.user?.displayName}`);
      console.log(`✅ Quota usada: ${aboutResponse.data.storageQuota?.usage} bytes`);
      console.log(`✅ Quota total: ${aboutResponse.data.storageQuota?.limit} bytes\n`);
    } catch (error) {
      console.log(`❌ Erro ao obter informações da conta: ${error.message}\n`);
      return;
    }

    // Teste 2: Acesso à pasta raiz
    console.log('📋 Teste 2: Verificação de acesso à pasta raiz');
    try {
      const rootResponse = await drive.files.get({
        fileId: rootFolderId!,
        fields: 'id,name,mimeType,owners,permissions',
      });

      console.log(`✅ Pasta encontrada: ${rootResponse.data.name}`);
      console.log(`✅ ID: ${rootResponse.data.id}`);
      console.log(`✅ Tipo: ${rootResponse.data.mimeType}`);
      
      if (rootResponse.data.owners) {
        console.log(`✅ Proprietário(s): ${rootResponse.data.owners.map(o => o.emailAddress).join(', ')}`);
      }
      
      if (rootResponse.data.permissions) {
        console.log(`✅ Permissões: ${rootResponse.data.permissions.length} item(s)`);
      }
      console.log();
    } catch (error) {
      console.log(`❌ Erro ao acessar pasta raiz: ${error.message}`);
      
      if (error.code === 404) {
        console.log('💡 Soluções possíveis:');
        console.log('   1. Verifique se o ID da pasta está correto');
        console.log('   2. Compartilhe a pasta com o email da conta de serviço');
        console.log('   3. Dê permissões de "Editor" ou "Proprietário" para a conta de serviço');
        console.log(`   4. Email da conta de serviço: ${await getServiceAccountEmail(auth)}`);
      }
      console.log();
      return;
    }

    // Teste 3: Listar arquivos da pasta raiz
    console.log('📋 Teste 3: Listagem de arquivos na pasta raiz');
    try {
      const listResponse = await drive.files.list({
        q: `"${rootFolderId}" in parents and trashed = false`,
        fields: 'files(id,name,mimeType,createdTime)',
        pageSize: 10,
      });

      const files = listResponse.data.files ?? [];
      console.log(`✅ Encontrados ${files.length} arquivo(s) na pasta raiz`);
      
      files.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.name} (${file.mimeType})`);
      });
      console.log();
    } catch (error) {
      console.log(`❌ Erro ao listar arquivos: ${error.message}\n`);
    }

    // Teste 4: Criar pasta de teste
    console.log('📋 Teste 4: Criação de pasta de teste dentro da pasta raiz');
    try {
      const testFolderName = `teste-${Date.now()}`;
      console.log(`   Criando pasta "${testFolderName}" dentro da pasta raiz (${rootFolderId})...`);
      
      const createResponse = await drive.files.create({
        requestBody: {
          name: testFolderName,
          mimeType: 'application/vnd.google-apps.folder',
          parents: [rootFolderId!],
        },
        fields: 'id',
      });

      const testFolderId = createResponse.data.id!;
      console.log(`✅ Pasta de teste criada: ${testFolderName} (ID: ${testFolderId})`);
      
      // Verificar se a pasta foi realmente criada na pasta raiz
      const verifyResponse = await drive.files.get({
        fileId: testFolderId,
        fields: 'id,name,parents',
      });
      
      if (verifyResponse.data.parents && verifyResponse.data.parents.includes(rootFolderId!)) {
        console.log(`✅ Pasta criada corretamente dentro da pasta raiz`);
        console.log(`   Parent ID verificado: ${verifyResponse.data.parents[0]}`);
      } else {
        console.log(`⚠️  Aviso: Pasta criada mas pode não estar na pasta raiz esperada`);
        console.log(`   Parents encontrados: ${verifyResponse.data.parents?.join(', ') || 'nenhum'}`);
        console.log(`   Pasta raiz esperada: ${rootFolderId}`);
      }
      
      // Verificar se a pasta aparece na listagem da pasta raiz
      const listAfterCreate = await drive.files.list({
        q: `"${rootFolderId}" in parents and name = "${testFolderName}" and trashed = false`,
        fields: 'files(id,name,mimeType)',
      });
      
      if (listAfterCreate.data.files && listAfterCreate.data.files.length > 0) {
        console.log(`✅ Pasta encontrada na listagem da pasta raiz`);
      } else {
        console.log(`⚠️  Pasta não encontrada na listagem da pasta raiz`);
      }

      // Limpar - deletar pasta de teste
      await drive.files.delete({ fileId: testFolderId });
      console.log(`✅ Pasta de teste removida\n`);
    } catch (error) {
      console.log(`❌ Erro ao criar pasta de teste: ${error.message}\n`);
    }

    console.log('🎉 Todos os testes concluídos com sucesso!');
    console.log('✅ A configuração do Google Drive está funcionando corretamente.');

  } catch (error) {
    console.log(`❌ Erro geral: ${error.message}`);
    console.log('\n💡 Verifique:');
    console.log('   1. As variáveis de ambiente GOOGLE_SERVICE_ACCOUNT_JSON ou GOOGLE_SERVICE_ACCOUNT_PATH');
    console.log('   2. O arquivo de credenciais da conta de serviço');
    console.log('   3. As permissões da API do Google Drive');
  }
}

async function getServiceAccountEmail(auth: any): Promise<string> {
  try {
    const drive = google.drive({ version: 'v3', auth });
    const response = await drive.about.get({ fields: 'user' });
    return response.data.user?.emailAddress || 'Não disponível';
  } catch {
    return 'Não foi possível obter';
  }
}

// Executar teste
testGoogleDriveConnection();
